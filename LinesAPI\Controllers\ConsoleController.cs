using Connectors.town.connectors.drivers;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange.town.connectors.drivers.artemis.processors;
using GamesEngine.Middleware;
using GamesEngine.Middleware.Providers;
using GamesEngine.RealTime;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Middleware.Synchronizator;
using static LinesAPI.Controllers.TournamentController;

namespace LinesAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LinesAPI.Lines.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, body);
            return result;
        }

        [HttpGet("console/scriptEnEjecucion")]
        [AllowAnonymous]
        public IActionResult ScriptEnEjecucion()
        {
            IActionResult result;
            try
            {
                result = Ok("Script executed: " + LinesAPI.Lines.ScriptEnEjecucion);
            }
            catch
            {
                result = Ok("Vuelva a ejecutar el request");
            }
            return result;
        }

        [HttpGet("console/officials")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> LoadOfficialNamesToSearchEngineAsync()
        {
            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext,
                $@"
                {{
                    tournamentsList = tournaments.List();
					for ( tournaments : tournamentsList )
					{{
						print tournaments.Id id;
						print tournaments.Description description;
                        gamesList = tournaments.Games();
                        for (games : gamesList)
                        {{
                            teama = games.TeamA;
                            teamb = games.TeamB;
                            print games.Sport.Id sportId;
				            print games.Sport.Name sportName;
				            theSportHasAnyAlias = games.Sport.Aliases.HasAnyForeingAlias();
				            print theSportHasAnyAlias theSportHasAnyAlias;
				            if(theSportHasAnyAlias)
				            {{
					            for(sporAliases : games.Sport.Aliases.GetAllForeingAlias())
					            {{
						            print sporAliases.Name name;
						            print sporAliases.Id id;
						            print sporAliases.Provider.Id providerId;
						            print sporAliases.Provider.Name providerName;
					            }}
				            }}

				            print games.Tournament.League.Id leagueId;
				            print games.Tournament.League.Name leagueName;
				            print games.Tournament.League.Name leagueShortName;
				            theLeagueHasAnyAlias = games.Tournament.League.Aliases.HasAnyForeingAlias();
				            print theLeagueHasAnyAlias theLeagueHasAnyAlias;
				            if(theLeagueHasAnyAlias)
				            {{
					            for(leagueAliases :  games.Tournament.League.Aliases.GetAllForeingAlias())
					            {{
						            print leagueAliases.Name name;
						            print leagueAliases.Id id;
						            print leagueAliases.Provider.Id providerId;
						            print leagueAliases.Provider.Name providerName;
					            }}
				            }}

				            print games.Tournament.Id tournamentId;
				            theTournamentHasAnyAlias = games.Tournament.Aliases.HasAnyForeingAlias();
				            print theTournamentHasAnyAlias theTournamentHasAnyAlias;
				            if(theTournamentHasAnyAlias)
				            {{
					            for(tournamentAliases :  games.Tournament.Aliases.GetAllForeingAlias())
					            {{
						            print tournamentAliases.Name name;
						            print tournamentAliases.Id id;
						            print tournamentAliases.Provider.Id providerId;
						            print tournamentAliases.Provider.Name providerName;
					            }}
				            }}

				            print games.Number gameNumber;
				            isPregame = games.IsPregame();
				            print isPregame isPregame;
				            if( ! isPregame)
				            {{
					            print games.ScheduledDate gameOpenDate;
				            }}
                            gameHasAnyAlias = games.Aliases.HasAnyForeingAlias();
				            print gameHasAnyAlias gameHasAnyAlias;
				            if(gameHasAnyAlias)
				            {{
					            for(gameAliases :  games.Aliases.GetAllForeingAlias())
					            {{
						            print gameAliases.Name name;
						            print gameAliases.Id id;
						            print gameAliases.Provider.Id providerId;
						            print gameAliases.Provider.Name providerName;
					            }}
				            }}

				            print teama.Name teamAName;
				            print teama.ShortName teamAShortName;
				            print teama.League.Id teamALeagueId;
				            theTeamaHasAnyAlias = (boolean)teama.Aliases.HasAnyForeingAlias();
				            print theTeamaHasAnyAlias theTeamaHasAnyAlias;
				            if(theTeamaHasAnyAlias)
				            {{
					            for(teamaAliases : teama.Aliases.GetAllForeingAlias())
					            {{
						            print teamaAliases.Name name;
						            print teamaAliases.Id id;
						            print teamaAliases.Provider.Id providerId;
						            print teamaAliases.Provider.Name providerName;
					            }}
				            }}

				            print teamb.Name teamBName;
				            print teamb.ShortName teamBShortName;
				            print teamb.League.Id teamBLeagueId;
				            theTeambHasAnyAlias = teamb.Aliases.HasAnyForeingAlias();
				            print theTeambHasAnyAlias theTeambHasAnyAlias;
				            if(theTeambHasAnyAlias)
				            {{
					            for(teambAliases : teamb.Aliases.GetAllForeingAlias())
					            {{
						            print teambAliases.Name name;
						            print teambAliases.Id id;
						            print teambAliases.Provider.Id providerId;
						            print teambAliases.Provider.Name providerName;
					            }}
				            }}
						}}
                    }}
                }}
                ");

            string json = ((OkObjectResult)result).Value.ToString();
            var document = JsonConvert.DeserializeObject<TournamentsRelatedInformation>(json);
            foreach (var tournamentInfo in document.Tournaments)
            {
                foreach (var gameInfo in tournamentInfo.Games)
                {
                    Synchronizator.CreateOrUpdateOfficialDocuments(gameInfo);

                }
            }

            return result;
        }

        [HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();

            IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        [HttpGet("consumer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopConsumerAsync()
        {
            await Consumer.StopAllConsumerActiveInMemoryAsync();
            return Ok("All consumers are stopped!");
        }

        [HttpGet("consumer/start")]
        [AllowAnonymous]
        public IActionResult StartConsumer()
        {
            Integration.UseKafka = true;
            if (Integration.UseKafka)
            {
                new Consumers().CreateConsumerForTopics();
            }
            return Ok("Consumers restarted!");
        }

        const string ClientLines = "Lines";
        [HttpGet("api/security/roles")]
        [Authorize(Roles = "l110,l111")]
        public async Task<IActionResult> GetRolesAsync()
        {
            return Ok(await Security.ClerksAPI().GetAvailableRealmRolesAsync(ClientLines));
        }

        [HttpGet("api/security/users")]
        [Authorize(Roles = "v17")]
        public async Task<IActionResult> GetInternalUsersAsync(string search, int first, int max)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientLines, search, first, max);

            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
                var internalUsers = users.Where(
                        x => x.HasEmail &&
                        !Security.ClerksAPI().IsDefaultUser(ClientLines, x.Username)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/security/users/count")]
        [Authorize(Roles = "v17")]
        public async Task<IActionResult> CountInternalUsersAsync()
        {
            return await Security.ClerksAPI().CountUsersAsync(ClientLines);
        }

        [HttpPost("api/security/users")]
        [Authorize(Roles = "l110")]
        public async Task<IActionResult> CreateUserAsync([FromBody] UserInternal user)
        {
            User userKc = new User()
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Enabled = user.Enabled,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Credentials = user.Credentials
            };

            User securedUser = await GetUserFromSecurityServerAsync(userKc);
            if (securedUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because username {user.Username} was already taken.""}}");

            User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(userKc);
            if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because email {user.Email} was already taken.""}}");

            var result = await LinesAPI.Lines.PerformQryAsync(HttpContext, $@"
            {{
                print company.System.Tenants.CurrentTenant.Id tenantId;
                print company.System.Tenants.CurrentTenant.Name tenantName;
                print company.Sales.CurrentStore.Id storeId;
            }}
            ");

            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var tenant = JsonConvert.DeserializeObject<TenantData>(json);

            userKc.Attributes = new Attributes()
            {
                TenantId = new[] { tenant.TenantId.ToString() }
            };

            result = await Security.ClerksAPI().CreateAgentsAsync(ClientLines, userKc);

            if (result is OkObjectResult)
            {
                User userToReturn = await GetUserFromSecurityServerAsync(userKc);
                if (userToReturn != null)
                {
                    Password pass = new Password();
                    pass.Type = "password";
                    pass.Value = user.Credentials[0].Value;
                    pass.Temporary = true;

                    result = await ResetPasswordAsync(userToReturn.Id, pass);
                    if (result is OkObjectResult)
                    {
                        return Ok(userToReturn);
                    }
                    return result;
                }
                else
                {
                    return BadRequest($@"User {JsonConvert.SerializeObject(user)} was not created.");
                }
            }
            return result;
        }

        [HttpGet("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
            return (paymentProcessor.Driver as IFragmentDriver).AmountOfWagersPerChunk;
        }

        [HttpPut("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeAmountOfWagerPerChunk([FromBody] AmountOfWagersPerChunkBody body)
        {
            var paymentProcessors = WholePaymentProcessor.Instance().SearchFragmentProcessors();
            foreach (var processor in paymentProcessors)
            {
                (processor.Driver as IFragmentDriver).AmountOfWagersPerChunk = body.AmountOfWagersPerChunk;
            }

            return body.AmountOfWagersPerChunk;
        }

        [HttpGet("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetArtemisAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Deposit));
            return ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk;
        }

        [HttpPut("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeArtemisAmountOfWagerPerChunk([FromBody] AmountOfWagersPerChunkBody body)
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Deposit));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;
            paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Withdrawal));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;
            return body.AmountOfWagersPerChunk;
        }

        [HttpPost("api/lines/listener")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectSignalRAsync([FromBody] SignalRRegisterBody body)
        {
            ProxyListener listener = new ProxyListener(body.IpAddress, body.Port, body.Protocol);
            await listener.ConnectAsync();
            PlatformMonitor.GetInstance().Register(listener);
            return Ok();
        }

        [HttpPost("api/lines/listeners")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectListenersAsync([FromBody] ListenerRegistrationBody body)
        {
            foreach (var subset in body.subsets)
            {
                if (subset.ports.Length > 1) return BadRequest($@"Only one port is allowed for all connections.");
                var port = subset.ports.First().port;
                var protocol = subset.ports.First().name;
                foreach (var address in subset.addresses)
                {
                    ProxyListener listener = new ProxyListener(address.ip, port, protocol);
                    await listener.ConnectAsync();
                    PlatformMonitor.GetInstance().Register(listener);
                }
            }

            return Ok();
        }

        [HttpGet("api/lines/listeners")]
        [AllowAnonymous]
        public IActionResult GetListeners()
        {
            var result = PlatformMonitor.GetInstance().Listeners;
            return Ok(result);
        }

        private async Task<User> GetUserFromSecurityServerAsync(User user)
        {
            var result = await Security.ClerksAPI().GetByUsernameAsync(ClientLines, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Username.ToLower().Equals(user.Username.ToLower())) return userToCheck;
            }
            return null;
        }

        private async Task<User> GetUserFromEmailSecurityServerAsync(User user)
        {
            var result = await Security.ClerksAPI().GetByEmailAsync(ClientLines, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Email.ToLower().Equals(user.Email.ToLower())) return userToCheck;
            }
            return null;
        }

        [HttpGet("api/security/users/{userID}")]
        [Authorize(Roles = "v17")]
        public async Task<IActionResult> GetUserAsync(string userID)
        {
            return await Security.ClerksAPI().GetAgentsAsync(ClientLines, userID);
        }

        [HttpGet("api/security/users/{userID}/roles")]
        [Authorize(Roles = "l111")]
        public async Task<IActionResult> GetRolesOFUserAsync(string userID)
        {
            var roles = await Security.ClerksAPI().GetRolesOFUserAsync(ClientLines, userID);
            return Ok(roles);
        }

        [HttpPost("api/security/users/{userID}/roles")]
        [Authorize(Roles = "l110,l111")]
        public async Task<IActionResult> MapRoleToUsersAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().MapRoleToUsersAsync(ClientLines, userID, roleMapping);
        }

        [HttpPost("api/security/users/{userID}/logout")]
        [AllowAnonymous]
        public async Task<IActionResult> LogoutAsync(string userID)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientLines, userID);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(jsonResult);

                result = await Security.ClerksAPI().LogOutAsync(ClientLines, userID);
                if (result is OkObjectResult)
                {
                    LogOutResponse output = new LogOutResponse();
                    output.HasEmail = user.HasEmail;
                    return Ok(output);
                }
            }

            return result;
        }

        [HttpPut("api/security/users/{userID}/rolesremoval")]
        [Authorize(Roles = "l110,l111")]
        public async Task<IActionResult> MapRoleToUsersDeleteAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().DeleteMapRoleToUsersAsync(ClientLines, userID, roleMapping);
        }

        [HttpPut("api/security/users/{userID}")]
        [Authorize(Roles = "l111")]
        public async Task<IActionResult> UpdateUserAsync(string userID, [FromBody] User user)
        {
            User userValue = await GetUserFromSecurityServerAsync(user);
            if (userValue != null)
            {
                if (userValue.Email != user.Email)
                {
                    User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(user);

                    if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not updated because email {user.Email} was already taken.""}}");
                }
            }
            return await Security.ClerksAPI().UpdateUserAsync(ClientLines, userID, user);
        }

        [HttpPut("api/security/users/{userID}/reset-password")]
        [Authorize(Roles = "l111")]
        public async Task<IActionResult> ResetPasswordAsync(string userID, [FromBody] Password pass)
        {
            return await Security.ClerksAPI().UpdatePasswordAsync(ClientLines, userID, pass);
        }


        [HttpDelete("api/security/users/{userID}")]
        [Authorize(Roles = "l112")]
        public async Task<IActionResult> DeleteUserAsync(string userID)
        {
            return await Security.ClerksAPI().DeleteUserAsync(ClientLines, userID);
        }

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }

        [HttpGet("console/demo")]
        [AllowAnonymous]
        public async Task<IActionResult> DemoAsync()
        {
            JObject finalResult = new JObject();
            int providerId = 2;
            var idSport = 1;
            var competitionsResult = await RequestLeaguesDataInTheProviderAsync(idSport);
            if (competitionsResult is OkObjectResult)
            {
                JArray unsortedCompetitionsObject = (JArray)JArray.Parse((competitionsResult as OkObjectResult).Value.ToString()).First;//Soccer only

                JArray competitionsObjectfull = new JArray(unsortedCompetitionsObject.OrderByDescending(obj => (int)obj["marketCount"]));

                JArray competitionsObject = new JArray();
                competitionsObject.Add(competitionsObjectfull[0]);
                competitionsObject.Add(competitionsObjectfull[1]);
                finalResult.Add("competitions", competitionsObject);

                var competitionsIds = new List<string>();
                var competitionsNames = new List<string>();
                foreach (var competitionData in competitionsObject.Children<JObject>())
                {
                    var competitionName = Validator.StringEscape(competitionData["competition"]["name"].ToString());
                    var competitionId = competitionData["competition"]["id"].ToString();
                    competitionsIds.Add(competitionId);
                    competitionsNames.Add(competitionName);
                }
                var script = new StringBuilder();
                for (var i = 0; i < competitionsIds.Count; i++)
                {
                    var competitionId = competitionsIds[i];
                    var competitionName = Validator.StringEscape(competitionsNames[i]);

                    var resultCompetition = LinesAPI.Lines.PerformCmd($@"
                            sport = company.Tournaments.Sports.FindById({idSport});
				            leagueId = tournaments.Leagues.NextLeagueId;
				            tournamentId = tournaments.NextTournamentId;
				            league = tournaments.Leagues.Create(sport, '{competitionName}', '{competitionName.Substring(0, 2)}{competitionId}', leagueId);
                            league.Aliases.Add(ForeingAlias({providerId}, '{competitionId}', '{competitionName}'));
                            tournament = tournaments.CreateTournament(league, tournamentId, '{competitionName} 2021');
                            betBoard = company.Betboard(tournament);
                            tournament.OpenRegistration();
                            print leagueId leagueId;
                            print tournament.Id tournamentId;
                    ");
                    if (!(resultCompetition is OkObjectResult))
                    {
                        throw new Exception($@"Error:{((ObjectResult)resultCompetition).Value.ToString()}");
                    }
                    string competitionJson = ((OkObjectResult)resultCompetition).Value.ToString();
                    dynamic competitionResult = JsonConvert.DeserializeObject<dynamic>(competitionJson);
                    int leagueId = Convert.ToInt32(competitionResult.leagueId);
                    int tournamentId = Convert.ToInt32(competitionResult.tournamentId);

                    var eventsResult = await RequestMatchesDataInTheProviderAsync(new List<string> { competitionId.ToString() }, new List<int> { idSport });
                    var eventsObject = JArray.Parse((eventsResult as OkObjectResult).Value.ToString());
                    finalResult.Add("teams" + i, eventsObject);

                    Dictionary<string, JToken> idByteamName = new Dictionary<string, JToken>();
                    foreach (var eventsData in eventsObject.Children())
                    {
                        foreach (var eventData in eventsData.Children())
                        {
                            var eventName = Validator.StringEscape(eventData["event"]["name"].ToString());
                            var eventId = eventData["event"]["id"].ToString();
                            const string token = " v ";
                            string[] teams = NormalizedProviderResponse.SplitEventName(token, eventName);

                            SaveTeam(idByteamName, teams[0], leagueId);
                            SaveTeam(idByteamName, teams[1], leagueId);

                        }
                    }

                    foreach (var eventsData in eventsObject.Children())
                    {
                        foreach (var eventData in eventsData.Children())
                        {
                            var eventName = Validator.StringEscape(eventData["event"]["name"].ToString());
                            var eventId = eventData["event"]["id"].ToString();
                            const string token = " v ";
                            string[] teams = NormalizedProviderResponse.SplitEventName(token, eventName);
                            DateTime eventDate = FechaHora.ParseToLocalTime(eventData["event"]["openDate"].ToString());
                            string startDate = $"{eventDate.Month}/{eventDate.Day}/{eventDate.Year} {eventDate.Hour}:{eventDate.Minute}:{eventDate.Second}";
                            string matchDate = $"{eventDate.Month}/{eventDate.Day}/{eventDate.Year}";
                            if (eventDate.Hour == 0 && eventDate.Hour == 0 && eventDate.Second == 0) startDate = matchDate + " 00:00:01";
                            script.Append($@"
                                league = tournaments.Leagues.FindById({leagueId});
                                tournament = tournaments.FindById({tournamentId});
				                teama = tournaments.FindTeamById({idByteamName[teams[0]]});   
				                teamb = tournaments.FindTeamById({idByteamName[teams[1]]});  
                                Eval('gameNumber = ' + tournament.NextGameNumber() + ';');
				                game = tournament.GetNewGame(gameNumber,teama,teamb, {startDate});
                                game.Aliases.Add(ForeingAlias({providerId}, '{eventId}', '{eventName}'));
				                game.Home = game.TeamA;
				                game.Visitor = game.TeamB;
				                game.SetFavorite(game.TeamA);
                                betBoard = company.Betboard(tournament);
					            matchDay = betBoard.Matchday({matchDate});
			                ");
                        }
                    }
                }

                finalResult.Add("scriptLeaguesAndTournamet", script.ToString());
                var result = LinesAPI.Lines.PerformCmd($@"
				            {script.ToString()}
			            ");
                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value.ToString()}");
                }

                result = LinesAPI.Lines.PerformQry($@"
                {{
                    tournamentsList = (List<Tournament>)tournaments.List();
				    for ( tournaments : tournamentsList )
				    {{
					    tournament = (Tournament)tournaments;
					    print tournament.Id id;
                        print tournament.Description tournamentDescription;
				        gamesList = (Games)tournament.Games();
                        for (games : gamesList)
                        {{
					        game = (Game)games;
                            print game.Number gameNumber;
                            print game.Sport.Id sportId;
                            print game.Sport.Name sportName;
                            for(gameAliases :  game.Aliases.GetAllForeingAlias())
				            {{
					            print gameAliases.Id id;
					            print gameAliases.Provider.Id providerId;
				            }}
                        }}
                    }}
                }}
                ");
                if (!(result is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)result).Value.ToString()}");
                }
                string json = ((OkObjectResult)result).Value.ToString();

                var tournamentsInfo = JsonConvert.DeserializeObject<TournamentsInformation>(json);
                finalResult.Add("tournamentsInfo", json);
                foreach (var tournamentInfo in tournamentsInfo.Tournaments)
                {
                    if (tournamentInfo.Games != null)
                    {
                        foreach (var game in tournamentInfo.Games)
                        {
                            if (game.Aliases != null) await RequestLinesDataInTheProviderAsync(game, tournamentInfo.TournamentId);
                        }
                    }
                }
                finalResult.Add("finish", true);
            }

            return Ok(finalResult);
        }

        private void SaveTeam(Dictionary<string, JToken> idByteamName, string team, int leagueId)
        {
            if (!idByteamName.ContainsKey(team))
            {
                var resultTeam = LinesAPI.Lines.PerformQry($@" 
                {{
                    league = tournaments.Leagues.FindById({leagueId});
                    exists = tournaments.ExistsTeam(league, '{team}');
                    print exists exists;
                    if(exists)
                    {{
                        team = tournaments.SearchTeam(league, '{team}');
                        print team.Id teamId;
                    }}
                    }}
                ");
                if (!(resultTeam is OkObjectResult))
                {
                    throw new Exception($@"Error:{((ObjectResult)resultTeam).Value.ToString()}");
                }
                string teamJson = ((OkObjectResult)resultTeam).Value.ToString();
                dynamic teamJsonResult = JsonConvert.DeserializeObject<dynamic>(teamJson);
                if (Convert.ToBoolean(teamJsonResult.exists))
                {
                    idByteamName.Add(team, Convert.ToInt32(teamJsonResult.teamId));
                }
                else
                {
                    resultTeam = LinesAPI.Lines.PerformCmd($@"
                                    league = tournaments.Leagues.FindById({leagueId});
                                    Eval('teamId =' + tournaments.NextTeamId() + ';');
				                    team = tournaments.CreateTeam(league, teamId, '{team}', '{team}');
				                    tournament.Register(team);
                                    print team.Id teamId;
                                ");
                    if (!(resultTeam is OkObjectResult))
                    {
                        throw new Exception($@"Error:{((ObjectResult)resultTeam).Value.ToString()}");
                    }
                    teamJson = ((OkObjectResult)resultTeam).Value.ToString();

                    teamJsonResult = JsonConvert.DeserializeObject<dynamic>(teamJson);
                    idByteamName.Add(team, Convert.ToInt32(teamJsonResult.teamId));
                }

            }
        }

        async Task<IActionResult> RequestLeaguesDataInTheProviderAsync(int idSport)
        {
            IActionResult result;
            try
            {
                var url = $"{Settings.LinesETLUrl}api/jobs/betfair/leagues?idSport=" + idSport;
                HttpRestClientConfiguration restClientToCashier = HttpRestClientConfiguration.GetInstance();
                result = await restClientToCashier.GetAsync(url);
                if (!(result is OkObjectResult))
                {
                    string errorBody = string.Empty;
                    if (result is ContentResult)
                    {
                        errorBody = $@"error:{((ObjectResult)result).ToString()} \n url:{url} ";
                    }
                    else
                    {
                        errorBody = $@"url:{url} failed ";
                    }
                    ErrorsSender.Send(errorBody, $"Request {url} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
                return new BadRequestResult();
            }
            return result;
        }

        async Task<IActionResult> RequestMatchesDataInTheProviderAsync(List<string> competitionsIds, List<int> sportIds)
        {
            if (competitionsIds.Count != sportIds.Count) throw new GameEngineException($"{nameof(competitionsIds)} must have the same size than {nameof(sportIds)}");
            IActionResult result;
            int providerId = 2;
            var paylod = new TriggerTeamsPayload();
            List<int> providerIds = new List<int>();
            List<string> leagueIdTheProvider = new List<string>();

            for (int i = 0; i < competitionsIds.Count; i++)
            {
                providerIds.Add(providerId);
                leagueIdTheProvider.Add(competitionsIds[i]);
            }
            paylod.ProviderIds = providerIds.ToArray();
            paylod.LeagueIdTheProvider = leagueIdTheProvider.ToArray();
            paylod.SportIds = sportIds.ToArray();
            try
            {
                var url = $"{Settings.LinesETLUrl}api/jobs/all/leagues/matches";
                HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
                result = await restClient.PostAsync(url, paylod);
                if (!(result is OkObjectResult))
                {
                    string errorBody = string.Empty;
                    if (result is ContentResult)
                    {
                        errorBody = $@"error:{((ObjectResult)result).ToString()} \n url:{url} \n providers:{string.Join(',', paylod.ProviderIds)} \n leagues:{string.Join(',', paylod.ProviderIds)}";
                    }
                    else
                    {
                        errorBody = $@"url:{url} failed \n providers:{string.Join(',', paylod.ProviderIds)} \n leagues:{string.Join(',', paylod.ProviderIds)}";
                    }
                    ErrorsSender.Send(errorBody, $"Request {url} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
                return new BadRequestResult();
            }

            return result;
        }

        async Task<IActionResult> RequestLinesDataInTheProviderAsync(GameInformation body, int tournamentId)
        {
            IActionResult result;
            var paylod = new TriggerLinesPayload();
            List<int> providerIds = new List<int>();
            List<string> leagueIdTheProvider = new List<string>();

            for (int i = 0; i < body.Aliases.Count; i++)
            {
                providerIds.Add(body.Aliases[i].ProviderId);
                leagueIdTheProvider.Add(body.Aliases[i].Id);
            }
            paylod.ProviderIds = providerIds.ToArray();
            paylod.MatchIdsInTheProvider = leagueIdTheProvider.ToArray();
            paylod.TournamentId = tournamentId;
            paylod.GameId = body.GameId;
            try
            {
                var url = $"{Settings.LinesETLUrl}api/jobs/all/leagues/matches/lines";
                HttpRestClientConfiguration restClient = HttpRestClientConfiguration.GetInstance();
                result = await restClient.PostAsync(url, paylod);
                if (!(result is OkObjectResult))
                {
                    string errorBody = string.Empty;
                    if (result is ContentResult)
                    {
                        errorBody = $@"error:{((ObjectResult)result).ToString()} \n url:{url} \n providers:{string.Join(',', paylod.ProviderIds)} \n leagues:{string.Join(',', paylod.ProviderIds)}";
                    }
                    else
                    {
                        errorBody = $@"url:{url} failed \n providers:{string.Join(',', paylod.ProviderIds)} \n leagues:{string.Join(',', paylod.ProviderIds)}";
                    }
                    ErrorsSender.Send(errorBody, $"Request {url} fails.");
                }
            }
            catch (Exception e)
            {
                ErrorsSender.Send(e);
                return new BadRequestResult();
            }

            return result;
        }

        [DataContract(Name = "TournamentsInformation")]
        public class TournamentsInformation
        {
            [DataMember(Name = "tournaments")]
            public List<TournamentInformation> Tournaments { get; set; }
        }

        [DataContract(Name = "TournamentInformation")]
        public class TournamentInformation
        {
            [DataMember(Name = "id")]
            public int TournamentId { get; set; }
            [DataMember(Name = "tournamentDescription")]
            public string TournamentDescription { get; set; }

            [DataMember(Name = "games")]
            public List<GameInformation> Games { get; set; }
        }

        [DataContract(Name = "GameInformation")]
        public class GameInformation
        {
            [DataMember(Name = "gameNumber")]
            public int GameId { get; set; }
            [DataMember(Name = "sportId")]
            public int sportId { get; set; }
            [DataMember(Name = "sportName")]
            public string sportName { get; set; }
            [DataMember(Name = "gameAliases")]
            public List<AliasInformation> Aliases { get; set; }
        }

        [DataContract(Name = "AliasInformation")]
        public class AliasInformation
        {
            [DataMember(Name = "providerId")]
            public int ProviderId { get; set; }
            [DataMember(Name = "id")]
            public string Id { get; set; }
        }
    }
}

