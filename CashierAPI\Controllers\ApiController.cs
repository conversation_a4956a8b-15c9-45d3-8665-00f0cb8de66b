﻿using Connectors.town.connectors.driver.transactions;
using Connectors.town.connectors.drivers.consignment;
using Connectors.town.connectors.drivers.hades;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Exchange;
using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using town.connectors.commons;
using town.connectors.drivers.fiero;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.PaymentProcessorsAndActionsByDomains;
using static GamesEngine.Finance.Currencies;
using static GamesEngine.Finance.FragmentsCreationBody;
using static GamesEngine.Finance.PaymentChannels;
using static GamesEngine.Middleware.Providers.Response;
using static town.connectors.CustomSettings;
using BalancesResponse = GamesEngine.Finance.BalancesResponse;
using Deposit = Connectors.town.connectors.drivers.consignment.Deposit;
using DepositTransaction = Connectors.town.connectors.driver.transactions.DepositTransaction;
using LockBalanceResponse = GamesEngine.Finance.LockBalanceResponse;
using WithdrawalTransaction = Connectors.town.connectors.driver.transactions.WithdrawalTransaction;

namespace CashierAPI.Controllers
{
	public class ApiController : AuthorizeController
	{
		[HttpGet("api/customers/{atAddress}/balances")]
		[Authorize(Roles = "player,a1")]
		public async Task<IActionResult> CustomerBalancesAsync(string atAddress)
        {
			if (string.IsNullOrEmpty(atAddress)) return NotFound("atAddress is required");

			Agents agent= Security.Agent(User);

			BalancesResponse response = await PaymentChannels.AllBalancesAsync(HttpContext, CashierAPI.Cashier, atAddress, agent);

			return Ok(response);
		}

        [HttpGet("api/customers/{atAddress}/lottoBalances")]
        [Authorize(Roles = "player,a1")]
        public async Task<IActionResult> AvailableCustomerBalancesAsync(string atAddress, string standardCurrency, string rewardCurrency)
        {
            if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest("atAddress is required");
			if (string.IsNullOrWhiteSpace(standardCurrency)) return BadRequest("standardCurrency is required");
			if (string.IsNullOrWhiteSpace(rewardCurrency)) return BadRequest("rewardCurrency is required");

            Agents agent = Security.Agent(User);
            BalancesResponse response = await PaymentChannels.SpecificBalancesAsync(HttpContext, CashierAPI.Cashier,  atAddress, agent, new string[] {standardCurrency, rewardCurrency});

            return Ok(response);
        }

        [HttpGet("api/customers/{atAddress}/balances/FP")]
		[Authorize(Roles = "mfp1,a15")]
		public async Task<IActionResult> CustomerFreePlayBalanceAsync(string atAddress)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					exists = atAddress.CheckIfExistsAccount('FP');
					if (exists)
					{{
						balance = atAddress.GetBalance('FP');
						print balance.Available balance;
					}}
					else
					{{
						print 0.0 balance;
					}}
				}}
			");

			return result;
		}

		private const string ALL_CURRENCIES = "all";
		[HttpGet("api/customers/{atAddress}/balances/{strCurrencyCode}/accounts")]
		[Authorize(Roles = "player,a12")]
		public async Task<IActionResult> CustomerBalancesWithAccountsAsync(string atAddress, string strCurrencyCode)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(strCurrencyCode)) return BadRequest($"{nameof(strCurrencyCode)} is required");

			Coin[] currencyCodes;
			if (strCurrencyCode == ALL_CURRENCIES)
            {
				currencyCodes = Coinage.All;
			}
            else
            {
				var currencyCode = Coinage.Coin(strCurrencyCode);
				currencyCodes = new Coin[]
				{
					currencyCode
				};
			}

			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceWithAccounts));
			BalancesResponse response = new BalancesResponse();
			foreach (var coin in currencyCodes)
            {
				BalanceResponse balanceResponse;
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("processorAlias", string.Empty);
					recordSet.SetParameter("atAddress", atAddress);
					recordSet.SetParameter("HttpContext", HttpContext);
					recordSet.SetParameter("currencyCode", coin.Iso4217Code);
					recordSet.SetParameter("actor", CashierAPI.Cashier);
					balanceResponse = await paymentProcessor.ExecuteAsync<BalanceResponse>(DateTime.Now, recordSet);
				}
				response.Add(balanceResponse);
			}

			return Ok(response);
		}

		[HttpGet("api/processors/balances")]
		[Authorize(Roles = "player,a12")]
		public async Task<IActionResult> ProcessorsBalancesAsync()
		{
			var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
				{{
					processorsWithDistinctKey = company.System.PaymentProcessor.SearchProcessorsWithDistinctKey();
					for (processors:processorsWithDistinctKey)
					{{
						processor = processors;
						print processor.Alias alias;
						print processor.CurrencyIso4217Code currencyCode;
						account = guardian.Accounts().SearchByProcessor(processor.ProcessorKey);
						print account.Number accountNumber;
					}}
				}}
				");
			if (!(result is OkObjectResult)) throw new GameEngineException($@"Error:{((ObjectResult)result).Value}");
			OkObjectResult o = (OkObjectResult)result;
			string json = o.Value.ToString();
			var processorsResponse = JsonConvert.DeserializeObject<ProcessorsResponse>(json);

			PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchBalanceProcesorBy(typeof(BalanceWithAccounts));
			BalancesResponse response = new BalancesResponse();
			foreach (var processor in processorsResponse.Processors)
			{
				BalanceResponse balanceResponse;
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("processorAlias", processor.Alias);
					recordSet.SetParameter("atAddress", processor.AccountNumber);
					recordSet.SetParameter("HttpContext", HttpContext);
					recordSet.SetParameter("currencyCode", processor.CurrencyCode);
					recordSet.SetParameter("actor", CashierAPI.Cashier);
					balanceResponse = await paymentProcessor.ExecuteAsync<BalanceResponse>(DateTime.Now, recordSet);
				}
				response.Add(balanceResponse);
			}

			return Ok(response);
		}

		public static PostFreeFormWager FragmentToWager(Fragment input)
		{
			return new PostFreeFormWager
			{
				BetDescription = input.BetDescription,
				WagerNumber = input.Number,
				ReferenceNumber = input.ReferenceNumber,
				Risk = input.Risk,
				ToWin = input.ToWin,
                TicketNumber = input.TicketNumber,
			};
		}

		const int FAKE_TICKET_NUMBER = -1;
		[HttpPost("api/customers/{atAddress}/balance/lock")]
		[Authorize(Roles = "player,c9,c13")]
		public async Task<IActionResult> LockAsync(string atAddress, [FromBody]LockBalanceData body)
		{
			if (body == null) return NotFound("Body is required.");
			if (string.IsNullOrEmpty(atAddress)) return NotFound("atAddress is required");

			try
			{
				Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.PurchaseTotal);
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchAuthorizationProcessorBy(typeof(AuthorizationInternal));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("actor", CashierAPI.Cashier);
					recordSet.SetParameter("context", HttpContext);
					recordSet.SetParameter("atAddress", atAddress);
					recordSet.SetParameter("purchaseTotal", purchaseTotal);
					recordSet.SetParameter("storeId", body.StoreId);
					recordSet.SetParameter("concept", body.Concept);
					recordSet.SetParameter("referenceNumber", body.Reference);
					recordSet.SetParameter("accountNumber", body.AccountNumber);
                    recordSet.SetParameter("useless", body.Useless);
                    recordSet.SetParameter("processorId", body.ProcessorId);
					recordSet.SetParameter("who", body.Who);
					recordSet.SetParameter("fragmentInformation", body.FragmentInformation);
					var result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
					return Ok(new LockBalanceResponse()
					{
						AuthorizationNumber = result.AuthorizationId,
						Code = result.Code
					}
					);
				}
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e, $@"atAddress:{atAddress}\n CurrencyCode: {body.CurrencyCode}\n PurchaseTotal:{body.PurchaseTotal}\n Store:{body.StoreId} Concept:{body.Concept}\n AccountNumber:{body.AccountNumber}");
				return Ok(new LockBalanceResponse() { AuthorizationNumber = FAKE_TICKET_NUMBER });
			}
		}

		[HttpPost("api/customers/{atAddress}/balance/externalMultiLock")]
		[Authorize(Roles = "player")]
		public async Task<IActionResult> ExternalMultiLockAsync(string atAddress, [FromBody] ExternalMultiLockBalanceData body)
		{
			if (body == null) return BadRequest("Body is required.");
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(body.CurrencyCode)) return BadRequest($"{nameof(body.CurrencyCode)} is required");

			try
			{
				Currency purchaseTotal = Currency.Factory(body.CurrencyCode, body.PurchaseTotal);
				PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchAuthorizationProcessorBy(typeof(AuthorizationExternalWithInternalBalance));
				using (RecordSet recordSet = paymentProcessor.GetRecordSet())
				{
					recordSet.SetParameter("actor", CashierAPI.Cashier);
					recordSet.SetParameter("context", HttpContext);
					recordSet.SetParameter("atAddress", atAddress);
					recordSet.SetParameter("purchaseTotal", purchaseTotal);
					recordSet.SetParameter("storeId", body.StoreId);
					recordSet.SetParameter("concept", body.Concept);
					recordSet.SetParameter("referenceNumber", body.Reference);
					recordSet.SetParameter("accountNumber", body.AccountNumber);
                    recordSet.SetParameter("useless", body.Useless);
                    recordSet.SetParameter("processorId", body.ProcessorId);
					recordSet.SetParameter("who", body.Who ?? "N/A");
					recordSet.SetParameter("fragmentInformation", body.FragmentInformation);
					recordSet.SetParameter("toWinsByDrawAndNumber", body.ToWinsByDrawAndNumber);
					var result = await paymentProcessor.ExecuteAsync<AuthorizationTransaction>(DateTime.Now, recordSet);
					return Ok(new LockBalanceResponse()
					{
						AuthorizationNumber = result.AuthorizationId,
						Code = result.Code
					}
					);
				}
			}
			catch (Exception e)
			{
				ErrorsSender.Send(e, $@"atAddress:{atAddress}\n CurrencyCode: {body.CurrencyCode}\n PurchaseTotal:{body.PurchaseTotal}\n Store:{body.StoreId} Concept:{body.Concept}\n AccountNumber:{body.AccountNumber}");
				return Ok(new LockBalanceResponse() { AuthorizationNumber = FAKE_TICKET_NUMBER });
			}
		}

		[HttpGet("api/customers/{atAddress}/{accountNumber}/existence")]
		[Authorize(Roles = "a30")]
		public async Task<IActionResult> ExistsAtAddressAsync(string atAddress, string accountNumber)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"{nameof(atAddress)} is required");
			if (string.IsNullOrWhiteSpace(accountNumber)) return BadRequest($"{nameof(accountNumber)} is invalid");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					existsBalance = atAddress.CheckIfExistsAccount('{accountNumber}');
					if (existsBalance)
					{{
						account = atAddress.SearchAccountByNumber('{accountNumber}');
						balance = account.Balance;
						print balance.Locked locked;
						print balance.Available balance;
						print balance.LockedAmount.ToDisplayFormat() lockedFormatted;
						print balance.AvailableAmount.ToDisplayFormat() balanceFormatted;
						print true foundAtAddress;
					}}
					else
					{{
						print false foundAtAddress;
					}}
				}}
			");

			return result;
		}

		[HttpGet("api/customers/{atAddress}/sources")]
		[Authorize(Roles = "a4")]
		public async Task<IActionResult> CustomerSourcesAsync(string atAddress)
		{
			if (string.IsNullOrWhiteSpace(atAddress)) return BadRequest($"Parameter {nameof(atAddress)} is required");

			var atAddressToUse = atAddress.ToLower().Trim();

			var result = await CashierAPI.Cashier.PerformQryAsync(atAddressToUse, HttpContext, $@"
				{{
					for(source : atAddress.ListSources())
					{{
						print source.Number id;
						print source.CurrencyAsText currency;
						print source.CurrencyTypeAsText type;
					}}
				}}
			");

			return result;
		}

		[HttpGet("api/sources")]
        [Authorize(Roles = "g15,devops,a5,FinancialReport")]
        public async Task<IActionResult> SourcesAsync()
		{
			return await CashierAPI.Cashier.PerformQryAsync("general", HttpContext, $@"
				{{
					balancesList = company.CustomerBalancesList;
					
					for(source : balancesList.ValidCurrencies())
					{{
						print source.Number id;
						print source.Name name;
						print source.CurrencyAsText currency;
						print source.CurrencyTypeAsText type;
					}}
				}}
			");
		}

		[HttpGet("api/currencies")]
		public async Task<IActionResult> CurrenciesAsync()
		{
			var result = await CashierAPI.Cashier.PerformQryAsync(MovementStorage.ATADDRESS_GENERAL, HttpContext, $@"
            {{
                for (currencies:company.System.EnabledCoins)
                {{
                    currency = currencies;
					print currency.Id id;
                    print currency.Iso4217Code currency;
                    print currency.UnicodeAsText unicode;
                    print currency.Name name;
					print currency.TypeAsText type;
                }}
            }}
            ");
			return result;
		}

		[HttpGet("api/movements/users")]
		[Authorize(Roles = "a17")]
		public async Task<IActionResult> usersAsync(int storeId)
		{
			var result = await CashierAPI.Cashier.PerformQryAsync("general", HttpContext, $@"
				{{
				    store = company.Sales.StoreById({storeId});
					for(users:store.Users())
					{{
						print users.Id id;
						print users.Name name;
					}}
				}}
			");

			return result;
		}

		public void CreateConsumerForTopics()
		{
			for (int index = 0; index < 10; index++)
			{
				new MovementsConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForMovements}_{index}").StartListening();
			}
			if (!Integration.OnlyMovementsConsumersInitialized)
			{
				new FragmentCreationConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentsCreation).StartListening();
				new UpdatedFragmentsSendingToAgentConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_FRAGMENTS_CONSUMER_SUFFIX}").StartListening();
				new UpdateUpdatedFragmentsSendingToAgentConsumer(Integration.Kafka.Group, $"{Integration.Kafka.TopicForFragmentPaymentsForAll}{KafkaMessage.SENDING_UPDATED_FRAGMENTS_CONSUMER_SUFFIX}").StartListening();
				new FragmentPaymentsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsForAll).StartListening();
				new FragmentPaymentsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForFragmentPaymentsForWinners).StartListening();
				new MovementsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForMovements).StartListening();
				new DepositsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForDeposits).StartListening();
			new CatalogConsumer(ValidateMessageOwnership, GetActor, $"{KafkaMessage.CASHIER_CONSUMER_PREFIX}{Integration.Kafka.Group}", Integration.Kafka.TopicForCatalog).StartListening();
#if DEBUG
				Task.Run(() =>
				{
					Thread.Sleep(20000); // This is avoid a race confition when mocks start up.
					new WithdrawalsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForWithdrawals).StartListening();
				});
#else
			new WithdrawalsConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForWithdrawals).StartListening();
#endif
			}
		}

		bool ValidateMessageOwnership(string storeAlias)
		{
			return false;
		}
		RestAPIActorAsync GetActor()
		{
			return CashierAPI.Cashier.GetActor(RestAPISpawnerActor.GENERAL);
		}

		public class FragmentCreationConsumer : GamesEngine.Settings.Consumer
		{

			public FragmentCreationConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				FragmentsCreationBody fragmentMessage = new FragmentsCreationBody(msg);

				Integration.MarkFragmentsTableAsCreated(fragmentMessage.AtAddress);
				Integration.MarkMovementsTableAsCreated(fragmentMessage.CurrencyCode);
				Integration.MarkMovementsTableAsCreated(fragmentMessage.AtAddress, Coinage.Coin(fragmentMessage.CurrencyCode));
				
				bool wasPayedInDollars = fragmentMessage.CurrencyCode == Currencies.CODES.USD.ToString();
				PostFreeFormWagerCollectionSuccessResponse result;
				var coin = Coinage.Coin(fragmentMessage.CurrencyCode);
				if (wasPayedInDollars)
				{
					var fragments = Array.ConvertAll(fragmentMessage.Fragments, new Converter<Fragment, PostFreeFormWager>(FragmentToWager));
                    PaymentProcessor paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor(typeof(town.connectors.drivers.fiero.Fragment_an_Authorization));
                    var isMultipleAuthorization = fragmentMessage.AuthorizationNumber == -1;
					if (isMultipleAuthorization)
					{
						PaymentChannels.DepositAndLockForMultipleAuthorizations(DateTime.Now, CashierAPI.Cashier, fragmentMessage);
						using (RecordSet recordSet = paymentProcessor.GetRecordSet())
						{
							recordSet.SetParameter("actor", CashierAPI.Cashier);
							recordSet.SetParameter("customerId", fragmentMessage.AtAddress);
							recordSet.SetParameter("ticketNumber", fragmentMessage.AuthorizationNumber);
							recordSet.SetParameter("wagers", fragments);
                            result = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
						}
					}
					else
					{
						PaymentChannels.DepositAndLock(DateTime.Now, CashierAPI.Cashier, fragmentMessage);
						using (RecordSet recordSet = paymentProcessor.GetRecordSet())
						{
							recordSet.SetParameter("actor", CashierAPI.Cashier);
							recordSet.SetParameter("customerId", fragmentMessage.AtAddress);
							recordSet.SetParameter("ticketNumber", fragmentMessage.AuthorizationNumber);
							recordSet.SetParameter("wagers", fragments);
							result = paymentProcessor.Execute<PostFreeFormWagerCollectionSuccessResponse>(DateTime.Now, recordSet);
						}
                    }
				}
				else
				{
					var wagers = Array.ConvertAll(fragmentMessage.Fragments, new Converter<Fragment, PostFreeFormWager>(FragmentToWager));
					var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
					result = Connectors.town.connectors.drivers.hades.Fragment_an_Authorization.CreateFakePostFreeFormWagerCollectionResponse(wagers, 1);

					var numbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
					int theLowestFragmentNumber = numbers.Min();
					int theHighestFragmentNumber = numbers.Max();
					paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor(typeof(FragmentAnAuthorizationAppended));
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("actor", CashierAPI.Cashier);
						recordSet.SetParameter("atAddress", fragmentMessage.AtAddress);
						recordSet.SetParameter("authorizationNumber", fragmentMessage.AuthorizationNumber);
						recordSet.SetParameter("theLowestFragmentNumber", theLowestFragmentNumber);
						recordSet.SetParameter("theHighestFragmentNumber", theHighestFragmentNumber);
						recordSet.SetParameter("fragmentsChunks", fragmentMessage.FragmentsChunks);
						paymentProcessor.Execute<object>(DateTime.Now, recordSet);
					}
					//((FieroAuthorizator)(Settings.AuthorizatorsAsync.authorizator)).AppendFragmentAuthorization(fragmentMessage.AtAddress, fragmentMessage.AuthorizationNumber, theLowestFragmentNumber, theHighestFragmentNumber, fragmentMessage.FragmentsChunks);
				}

				var wagerNumbers = result.Wagers.Select(x => int.Parse(x.WagerNumber));
				int theLowestWagerNumber = wagerNumbers.Min();
				int theHighestWagerNumber = wagerNumbers.Max();

				FragmentCreationResponse fragmentLockResponse = new FragmentCreationResponse(
					fragmentMessage.TheLowestBetId,
					fragmentMessage.TheHighestBetId,
					theLowestWagerNumber,
					theHighestWagerNumber,
					fragmentMessage.AuthorizationNumber,
					fragmentMessage.OrderNumber
				);

                var partition = Integration.Kafka.GetPartition(fragmentMessage.Localization);
                Integration.Kafka.Send(true, Integration.Kafka.TopicForFragmentsCreationCallback(fragmentMessage.StoreId), fragmentLockResponse, partition);
			}

		}

		public class FragmentPaymentsConsumer : GamesEngine.Settings.Consumer
		{
			private readonly string topic;

			public FragmentPaymentsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

			public override void OnMessageBeforeCommit(string comp)
			{
				if (String.IsNullOrEmpty(comp)) throw new Exception(nameof(comp));
				string msg = FragmentPaymentCompressor.Expand(comp);

				FragmentPaymentMessages payFragments = new FragmentPaymentMessages(msg);
				GradeFreeFormWagersResponse finalResponse = new GradeFreeFormWagersResponse();
				
				string who = payFragments.Owner;
				int storeId = payFragments.StoreId;
				bool sometimeWereTicketsSentToAccounting = payFragments.SometimeWereTicketsSentToAccounting;
				foreach (var currencyCode in payFragments.Currencies())
				{
					var fragments = payFragments.MessagesBy(currencyCode);
                    var payFragmentsArtemis = fragments.Where(wager => wager.AgentId == (int)Agents.ARTEMIS);
                    var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(GamesEngine.Exchange.town.connectors.drivers.artemis.processors.Deposit));
					string processorKey = null;
                    if (payFragmentsArtemis.Any()) processorKey = paymentProcessor.ProcessorKey;
                    paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(town.connectors.drivers.fiero.Grade));
					if (processorKey == null) processorKey = paymentProcessor.ProcessorKey;
					using (RecordSet recordSet = paymentProcessor.GetRecordSet())
					{
						recordSet.SetParameter("who", who);
						recordSet.SetParameter("payFragments", payFragments);
						recordSet.SetParameter("gradeFreeFormWagers", fragments);
						recordSet.SetParameter("actor", CashierAPI.Cashier);
                        recordSet.SetParameter("processorKey", processorKey);
                        var temp = paymentProcessor.Execute<GradeFreeFormWagersResponse>(DateTime.Now, recordSet);
						finalResponse.Merge(temp);
					}
					
					if (payFragmentsArtemis.Any())
					{
						ArtemisBehavior.SendFragmentsToUpdate(payFragmentsArtemis, sometimeWereTicketsSentToAccounting);
					}
				}

				if (finalResponse.Wagers != null && finalResponse.Wagers.Length > 0)
				{
					FragmentPaymentsWithProblemsResponse fragmentsWithProblems = new FragmentPaymentsWithProblemsResponse();
					foreach (var wager in finalResponse.Wagers)
					{
						if (!wager.IsValidTicketNumber)
						{
							fragmentsWithProblems.Add(wager.TicketNumber, wager.WagerNumber);
						}
					}

					if (fragmentsWithProblems.HasItems())
					{
                        var partition = Integration.Kafka.GetPartition(payFragments.Localization);
                        Integration.Kafka.Send(true, Integration.Kafka.TopicForFragmentPaymentsCallback(storeId), fragmentsWithProblems, partition);
					}
				}
			}
		}

		public class UpdatedFragmentsSendingToAgentConsumer : Consumer
		{
			public UpdatedFragmentsSendingToAgentConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

				var payFragments = new FragmentPaymentSendingToAgentMessages(msg);
				var fragments = payFragments.Fragments.ToList();
				PaymentChannels.UpdateWagers(fragments);
			}
		}

		public class UpdateUpdatedFragmentsSendingToAgentConsumer : Consumer
		{
			public UpdateUpdatedFragmentsSendingToAgentConsumer(string group, string topic) : base(group, topic)
			{
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (string.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

				var payFragments = new FragmentPaymentSendingToAgentMessages(msg);
				var fragments = payFragments.Fragments.ToList();
				PaymentChannels.UpdateUpdatedWagers(fragments);
			}
		}

		private class MovementsConsumer : Consumer
		{
			public MovementsConsumer(string group, string topic) : base(group, topic)
			{
				PrepareDataTables();
			}

			MovementsDeserialitor movementsDeserialitor = new MovementsDeserialitor();
			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrWhiteSpace(msg)) throw new Exception(nameof(msg));

				MovementsDeserialitor movementsDeserialitor = new MovementsDeserialitor();

				int whosId = Users.NO_USER;
				movementsDeserialitor.Separate(msg);
				if (movementsDeserialitor.Who != Users.LADYBET)
				{
					whosId = Movements.Storage.InsertUserIfNotExists(movementsDeserialitor.StoreId, movementsDeserialitor.Who);
				}

				Movements.Storage.SaveBalanceMovements(true, whosId, movementsDeserialitor.Movements, movementTableByAccount, movementTableByCurrency);
				movementsDeserialitor.Clear();
			}

			DataTable movementTableByAccount = new DataTable();
			DataTable movementTableByCurrency = new DataTable();
			void PrepareDataTables()
			{
				movementTableByAccount.Columns.Add("DAY", typeof(string));
				movementTableByAccount.Columns.Add("SOURCE", typeof(int));
				movementTableByAccount.Columns.Add("MOVEMENT", typeof(string));
				movementTableByAccount.Columns.Add("AMOUNT", typeof(decimal));
				movementTableByAccount.Columns.Add("NEWBALANCE", typeof(decimal));
				var dataColumn = movementTableByAccount.Columns.Add("WHO", typeof(int));
				dataColumn.AllowDBNull = true;
				movementTableByAccount.Columns.Add("DOCUMENTNUMBER", typeof(string));
				movementTableByAccount.Columns.Add("STORE", typeof(int));
				movementTableByAccount.Columns.Add("REFERENCE", typeof(string));
				movementTableByAccount.Columns.Add("CONCEPT", typeof(string));
				movementTableByAccount.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
				movementTableByAccount.Columns.Add("ACCOUNT_NUMBER", typeof(string));
				movementTableByAccount.Columns.Add("PROCESSORID", typeof(int));

				movementTableByCurrency.Columns.Add("DAY", typeof(string));
				movementTableByCurrency.Columns.Add("ATADDRESS", typeof(string));
				movementTableByCurrency.Columns.Add("SOURCE", typeof(int));
				movementTableByCurrency.Columns.Add("CURRENCY", typeof(string));
				movementTableByCurrency.Columns.Add("MOVEMENT", typeof(string));
				movementTableByCurrency.Columns.Add("AMOUNT", typeof(decimal));
				movementTableByCurrency.Columns.Add("NEWBALANCE", typeof(decimal));
				dataColumn = movementTableByCurrency.Columns.Add("WHO", typeof(int));
				dataColumn.AllowDBNull = true;
				movementTableByCurrency.Columns.Add("DOCUMENTNUMBER", typeof(string));
				movementTableByCurrency.Columns.Add("STORE", typeof(int));
				movementTableByCurrency.Columns.Add("REFERENCE", typeof(string));
				movementTableByCurrency.Columns.Add("CONCEPT", typeof(string));
				movementTableByCurrency.Columns.Add("NEWLOCKBALANCE", typeof(decimal));
				movementTableByCurrency.Columns.Add("ACCOUNT_NUMBER", typeof(string));
				movementTableByCurrency.Columns.Add("PROCESSORID", typeof(int));
			}
		}

		public class DepositsConsumer : Consumer
		{
			private string topic;
			public DepositsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				string[] messages = KafkaMessages.Split(msg);
				foreach (string codedMessage in messages)
				{
					string command = "";
					int authorization = 0;

					try
					{
						DepositMessage message = new DepositMessage(codedMessage);
						Integration.MarkFragmentsTableAsCreated(message.AtAddress);
						Integration.MarkMovementsTableAsCreated(message.Coin.Iso4217Code);
						Integration.MarkMovementsTableAsCreated(message.AtAddress, message.Coin);
						PaymentChannels.Deposit(true, DateTime.Now, CashierAPI.Cashier, message, out authorization);
	
					}
					catch (Exception e)
					{
						ErrorsSender.Send(e, $"message {msg}",  $"command:{command}", $"authorization:{authorization}");
					}	
				}
			}
		}

		public class WithdrawalsConsumer : Consumer
		{
			private string topic;
			public WithdrawalsConsumer(string group, string topic) : base(group, topic)
			{
				this.topic = topic;
			}

			public override void OnMessageBeforeCommit(string msg)
			{
				if (String.IsNullOrEmpty(msg)) throw new Exception(nameof(msg));

				string[] messages = KafkaMessages.Split(msg);
				foreach (string codedMessage in messages)
				{
					int authorization = 0;
					try
					{
						WithdrawMessage message = new WithdrawMessage(codedMessage);

						PaymentChannels.WithDraw(DateTime.Now, CashierAPI.Cashier, message, out authorization);
					}
					catch (Exception e)
					{
						ErrorsSender.Send(e, $"message {msg}", $"authorization:{authorization}");
					}
				}
			}
		}

		[DataContract(Name = "ProcessorsResponse")]
		public class ProcessorsResponse
		{
			[DataMember(Name = "processors")]
			public List<ProcessorWithDistinctKey> Processors { get; set; }
		}

		[DataContract(Name = "ProcessorWithDistinctKey")]
		public class ProcessorWithDistinctKey
		{
			[DataMember(Name = "alias")]
			public string Alias { get; set; }
			[DataMember(Name = "currencyCode")]
			public string CurrencyCode { get; set; }
			[DataMember(Name = "accountNumber")]
			public string AccountNumber { get; set; }
		}
	}
}
