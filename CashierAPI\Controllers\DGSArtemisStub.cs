using GamesEngine.Finance;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using town.connectors.drivers.artemis;
using static GamesEngine.Exchange.town.connectors.drivers.artemis.tenant.AccountInfo;

namespace CashierAPI.Controllers
{
	public class DGSArtemisStub : AuthorizeController
	{
		[HttpGet("/AccountInfo")]
		[AllowAnonymous]
		public AccountInfoBody GetAccountInfo(string name)
		{
			var result = new AccountInfoBody() { idPlayer = 406827, name = name };
			return result;
		}

		static int authorization = 500;
		[HttpPost("/transactions/non-posted")]
		[AllowAnonymous]
		public object PostNonPostedTransaction([FromBody] NonPostedTransactionRequestBody body)
		{
			authorization++;

			return new NonPostedTransactionSuccessResponse()
			{
				transactionId = authorization
			};
		}

		[HttpPost("/GradingBet")]
		[AllowAnonymous]
		public IActionResult GradeBet([FromBody] WagersUpdateBody body)
		{
			return new OkResult();
		}

		[HttpPost("/ReGradingBet")]
		[AllowAnonymous]
		public IActionResult ReGradeBet([FromBody] WagersUpdateBody body)
		{
			return new OkResult();
		}
	}

	public class NonPostedTransactionRequestBody
	{
		public int idPlayer { get; set; }
		public string description { get; set; }
		public decimal amount { get; set; }
		public string tranCode { get; set; }
		public string tranType { get; set; }
	}

	public class NonPostedTransactionSuccessResponse
	{
		public int transactionId { get; set; }
	}

	public class NonPostedTransactionErrorResponse
	{
		public string type { get; set; }
		public string title { get; set; }
		public int status { get; set; }
		public string detail { get; set; }
		public string instance { get; set; }
		public string additionalProp1 { get; set; }
		public string additionalProp2 { get; set; }
		public string additionalProp3 { get; set; }
	}
}
