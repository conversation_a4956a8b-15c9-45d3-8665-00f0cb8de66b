﻿using Connectors.town.connectors.drivers.artemis;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers.artemis;
using static Connectors.town.connectors.drivers.artemis.Fragment_an_Authorization;
using static GamesEngine.Finance.PaymentChannels;
using static town.connectors.CustomSettings;
using static town.connectors.CustomSettings.CustomSetting;
using static Connectors.town.connectors.drivers.artemis.ToWin;

namespace GamesEngineTests.Unit_Tests.Connectors
{
	[TestClass]
	public class ArtemisConnectorTest
	{
		[TestMethod]
		public async Task BalanceAsync()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new Balance();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("customerId", "123");
				var result = await driver.ExecuteAsync<decimal>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public void Fragment_an_Authorization()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new Fragment_an_Authorization();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("amount", 100);
				recordSet.SetParameter("customerId", "ABC5");
				recordSet.SetParameter("description", "Description");
				var artemisTickets = new List<ArtemisToWinByDrawAndNumber>();
				artemisTickets.Add(new ArtemisToWinByDrawAndNumber()
				{
					description = "description",
					draw = "GA",
					drawDate = "9/19/2022",
					drawHour = "12:14:00 PM",
					number = "111",
					pick = 3,
					risk = 0.25m,
					status = "",
					ticketId = "",
					toWin = 899,
					type = "2-way"
				});
				recordSet.SetParameter("toWinsByDrawAndNumber", artemisTickets);

				var result = driver.Execute<InsertWagersResponse>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public void Grade()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new Deposit();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				var fragments = new List<PayFragmentsMessage>();
				fragments.Add(new PayFragmentsMessage()
				{
					AdjustedLossAmount = "",
					AdjustedWinAmount = "0",
					AgentId = (int)Agents.ARTEMIS,
					DailyFigureDate_YYYYMMDD = "",
					Outcome = "L",
					TicketNumber = "123",
					WagerNumber = "1"
				});
				recordSet.SetParameter("wagers", fragments);

				var result = driver.Execute<InsertWagersResponse>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public void ReGrade()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new Withdrawal();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				var fragments = new List<PayFragmentsMessage>();
				fragments.Add(new PayFragmentsMessage()
				{
					AdjustedLossAmount = "",
					AdjustedWinAmount = "0",
					AgentId = (int)Agents.ARTEMIS,
					DailyFigureDate_YYYYMMDD = "",
					Outcome = "L",
					TicketNumber = "123",
					WagerNumber = "1"
				});
				recordSet.SetParameter("wagers", fragments);

				var result = driver.Execute<InsertWagersResponse>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public async Task ValidateCustomerAsync()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new ValidateCustomer();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("customerId", "123");
				recordSet.SetParameter("token", "123");
				var result = await driver.ExecuteAsync<bool>(DateTime.Now, recordSet);
			}
		}

		[TestMethod]
		public async Task ToWinAsync()
		{
			DGSProcessorDriver.AppToken = "token_test_123";
			//TODO disparar el docker-compose  cashier asi stub
			Variables variables = new Variables();
			DateTime now = DateTime.Now;
			CustomSettings cs = new CustomSettings(variables);
			cs.AddFixedParameter(now, "CompanyBaseUrlServices", "http://testart.sec-xm41d.com");
			cs.AddFixedParameter(now, "TokenSystemId", "3aab83fb");
			cs.AddFixedParameter(now, "TokenSystemPassword", new Secret("38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886"));

			var driver = new ToWin();
			driver.ConfigureThenPrepare(now, cs);
			using (RecordSet recordSet = driver.CustomSettings.GetRecordSet())
			{
				recordSet.SetParameter("playerId", "123");
				recordSet.SetParameter("pick", 3);
				var result = await driver.ExecuteAsync<ToWin.PayoutBody>(DateTime.Now, recordSet);
			}
		}
	}
}
