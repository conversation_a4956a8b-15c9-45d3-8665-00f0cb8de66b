using GamesEngine;
using Connectors.town.connectors.drivers;
using GamesEngine.Business;
using GamesEngine.RealTime;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.IO;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Finance.PaymentChannels;
using town.connectors.drivers.artemis;
using GamesEngine.Exchange.town.connectors.drivers.artemis.processors;

namespace LottoAPI.Controllers
{
    public class ConsoleController : AuthorizeController
    {
        [HttpPost("console/command")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processCommandAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LottoAPI.Actor.PerformCmdAsync(HttpContext, body);
            return result;
        }

        [HttpPost("console/query")]
        [Authorize(Roles = "devops")]
        public async Task<IActionResult> processQueryAsync()
        {
            string body = "";

            using (StreamReader reader = new StreamReader(Request.Body, Encoding.UTF8))
            {
                body = await reader.ReadToEndAsync();
            }

            if (body == null) return NotFound("Body is required");
            if (String.IsNullOrWhiteSpace(body)) return NotFound($"Parameter {nameof(body)} is required");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, body);
            return result;
        }

        [HttpGet("console/archive")]
        [Authorize(Roles = "devops")]
        public IActionResult AchiveActor(string startDate, string endDate)
        {
            if (!Validator.IsValidDate(startDate)) return NotFound($"Parameter {nameof(startDate)} is empty or invaid: {startDate}");
            if (!Validator.IsValidDate(endDate)) return NotFound($"Parameter {nameof(endDate)} is empty or invaid: {endDate}");

            DateTime sDate = DateTime.Parse(startDate);
            DateTime eDate = DateTime.Parse(endDate);

            var result = LottoAPI.Actor.PerformArchive(HttpContext, sDate, eDate);

            return result;
        }

        [HttpGet("console/trim")]
        [Authorize(Roles = "devops")]
        public IActionResult TrimActor(string trimmedDown)
        {
            if (!Validator.IsValidDate(trimmedDown)) return NotFound($"Parameter {nameof(trimmedDown)} is empty or invaid: {trimmedDown}");

            DateTime tDown = DateTime.Parse(trimmedDown);

            var result = LottoAPI.Actor.PerformTrim(HttpContext, tDown);

            return result;
        }

        [HttpGet("console/startFollower")]
        [Authorize(Roles = "devops")]
        public IActionResult StartFollower()
        {
            _ = Task.Run(() =>
            {
                LottoFollower.LottoFollower.MainFollower();
            });

            return Ok($"Runnig Lotto Follower...");
        }

        [HttpGet("console/dateTime")]
        [AllowAnonymous]
        public IActionResult GetDateTime()
        {
            return Ok(new DateTimeBody(DateTime.Now));
        }

        struct DateTimeBody
        {
            public string dateTime;
            public DateTimeBody(DateTime now)
            {
                dateTime = now.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }

        [HttpGet("console/scriptEnEjecucion")]
		[AllowAnonymous]
		public IActionResult ScriptEnEjecucion()
		{
			IActionResult result;
			try
			{
				result = Ok("Script executed: "+LottoAPI.Actor.ScriptEnEjecucion);
			}
			catch
			{
				result = Ok("Vuelva a ejecutar el request");
			}
			return result;
		}

        [HttpGet("console/ping")]
        [AllowAnonymous]
        public IActionResult Ping()
        {
            return Ok("pong");
        }

        [HttpGet("producer/stop")]
        [AllowAnonymous]
        public async Task<IActionResult> StopProducerAsync()
        {
			await Consumer.StopAllConsumerActiveInMemoryAsync();

			IProducer kafka = Integration.Kafka;
            if (kafka != null)
            {
                await Integration.Kafka.StopProducerAsync();
                return Ok();
            }
            return BadRequest("Kafka is not configured.");
        }

        const string ClientLotto = "lotto";
        const string ClientKeno = "keno";
        [HttpGet("api/lotto/security/roles")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> GetLottoRolesAsync()
        {
            return Ok(await Security.ClerksAPI().AvailableClientRolesAsync(ClientLotto));
        }

        [HttpGet("api/keno/security/roles")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> GetKenoRolesAsync()
        {
            return Ok(await Security.ClerksAPI().AvailableClientRolesAsync(ClientKeno));
        }

        [HttpGet("api/lotto/security/users")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> GetInternalLottoUsersAsync(string search, int first, int max)
        {
            var result = await Security.ClerksAPI().GetUsersAsync(ClientLotto, search, first, max);

            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
                var internalUsers = users.Where(
                        x => x.HasEmail &&
                        ! Security.ClerksAPI().IsDefaultUser(ClientLotto, x.Username)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/keno/security/users")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> GetInternalKenoUsersAsync(string search, int first, int max)
        {
            var result = await Security.ClerksAPI().GetUsersAsync(ClientKeno, search, first, max);

            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
                var internalUsers = users.Where(
                        x => x.HasEmail &&
                        !Security.ClerksAPI().IsDefaultUser(ClientKeno, x.Username)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/lotto/security/allusers")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> GetAllLottoUsersAsync()
        {
            var result = await Security.ClerksAPI().GetAllUsersAsync(ClientLotto);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
                var internalUsers = users.Where(
                        x => x.HasEmail &&
                        !Security.ClerksAPI().IsDefaultUser(ClientLotto, x.Username)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/keno/security/allusers")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> GetAllKenoUsersAsync()
        {
            var result = await Security.ClerksAPI().GetAllUsersAsync(ClientKeno);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
                var internalUsers = users.Where(
                        x => x.HasEmail &&
                        !Security.ClerksAPI().IsDefaultUser(ClientKeno, x.Username)
                    );

                return Ok(internalUsers);
            }
            return result;
        }

        [HttpGet("api/lotto/security/users/count")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> CountInternalLottoUsersAsync()
        {
            return await Security.ClerksAPI().CountUsersAsync(ClientLotto);
        }

        [HttpGet("api/keno/security/users/count")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> CountInternalKenoUsersAsync()
        {
            return await Security.ClerksAPI().CountUsersAsync(ClientKeno);
        }

        [HttpPost("api/lotto/security/users")]
        [Authorize(Roles = "CreateUser")]
        public async Task<IActionResult> CreateLottoUserAsync([FromBody]User user)
        {
            User securedUser = await GetUserFromSecurityServerAsync(ClientLotto, user);
			if (securedUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because username {user.Username} was already taken.""}}");

			User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(ClientLotto, user);
			if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because email {user.Email} was already taken.""}}");
            
            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print company.System.Tenants.CurrentTenant.Id tenantId;
                print company.System.Tenants.CurrentTenant.Name tenantName;
                print company.Sales.CurrentStore.Id storeId;
            }}
            ");

            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var tenant = JsonConvert.DeserializeObject<TenantData>(json);

            user.Attributes.TenantId = new[] { tenant.TenantId.ToString() };

            result = await Security.ClerksAPI().CreateAgentsAsync(ClientLotto, user);
            if (result is OkObjectResult)
            {
                User userToReturn = await GetUserFromSecurityServerAsync(ClientLotto, user);
                if (userToReturn != null)
                {
                    Password pass = new Password();
                    pass.Type = "password";
                    pass.Value = user.Credentials[0].Value;
                    pass.Temporary = true;

                    result = await ResetPasswordLottoAsync(userToReturn.Id, pass);
                    if (result is OkObjectResult)
                    {
                        return Ok(userToReturn);
                    }
                    return result;
                }
                else
                {
                    return BadRequest($@"User {JsonConvert.SerializeObject(user)} was not created.");
                }
            }
            return result;
        }

        [HttpPost("api/keno/security/users")]
        [Authorize(Roles = "KCreateUser")]
        public async Task<IActionResult> CreateKenoUserAsync([FromBody] User user)
        {
            User securedUser = await GetUserFromSecurityServerAsync(ClientKeno, user);
            if (securedUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because username {user.Username} was already taken.""}}");

            User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(ClientKeno, user);
            if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not created because email {user.Email} was already taken.""}}");

            var result = await LottoAPI.Actor.PerformQryAsync(HttpContext, $@"
            {{
                print company.System.Tenants.CurrentTenant.Id tenantId;
                print company.System.Tenants.CurrentTenant.Name tenantName;
                print company.Sales.CurrentStore.Id storeId;
            }}
            ");

            if (!(result is OkObjectResult))
            {
                return BadRequest($@"Error:{((ObjectResult)result).Value.ToString()}");
            }

            OkObjectResult o = (OkObjectResult)result;
            string json = o.Value.ToString();
            var tenant = JsonConvert.DeserializeObject<TenantData>(json);

            user.Attributes = new Attributes()
            {
                TenantId = new[] { tenant.TenantId.ToString() }
            };

            result = await Security.ClerksAPI().CreateAgentsAsync(ClientKeno, user);
            if (result is OkObjectResult)
            {
                User userToReturn = await GetUserFromSecurityServerAsync(ClientKeno, user);
                if (userToReturn != null)
                {
                    Password pass = new Password();
                    pass.Type = "password";
                    pass.Value = user.Credentials[0].Value;
                    pass.Temporary = true;

                    result = await ResetPasswordKenoAsync(userToReturn.Id, pass);
                    if (result is OkObjectResult)
                    {
                        return Ok(userToReturn);
                    }
                    return result;
                }
                else
                {
                    return BadRequest($@"User {JsonConvert.SerializeObject(user)} was not created.");
                }
            }
            return result;
        }

        [HttpGet("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchFragmentProcessor();
            return (paymentProcessor.Driver as IFragmentDriver).AmountOfWagersPerChunk;
        }

        [HttpPut("api/accountingservices/amountOfWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeAmountOfWagerPerChunk([FromBody]AmountOfWagersPerChunkBody body)
        {
            var paymentProcessors = WholePaymentProcessor.Instance().SearchFragmentProcessors();
            foreach (var processor in paymentProcessors)
            {
                (processor.Driver as IFragmentDriver).AmountOfWagersPerChunk = body.AmountOfWagersPerChunk;
            }
            
            return body.AmountOfWagersPerChunk;
        }

        [HttpGet("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int GetArtemisAmountOfWagerPerChunk()
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Deposit));
            return ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk;
        }

        [HttpPut("api/artemis/accountingservices/gradedWagersPerChunk")]
        [Authorize(Roles = "devops")]
        public int ChangeArtemisAmountOfWagerPerChunk([FromBody] AmountOfWagersPerChunkBody body)
        {
            var paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Deposit));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;
            paymentProcessor = WholePaymentProcessor.Instance().SearchGradeProcessorBy(typeof(Withdrawal));
            ((DGSTenantDriver)paymentProcessor.Driver).GradedWagersPerChunk = body.AmountOfWagersPerChunk;
            return body.AmountOfWagersPerChunk;
        }

        [HttpPost("api/listener")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectSignalRAsync([FromBody] SignalRRegisterBody body)
        {
            ProxyListener listener = new ProxyListener(body.IpAddress, body.Port, body.Protocol);
            await listener.ConnectAsync();
            PlatformMonitor.GetInstance().Register(listener);
            return Ok();
        }

        [HttpPost("api/listeners")]
        [AllowAnonymous]
        public async Task<IActionResult> ConnectListenersAsync([FromBody] ListenerRegistrationBody body)
        {
            foreach (var subset in body.subsets)
            {
                if (subset.ports.Length > 1) return BadRequest($@"Only one port is allowed for all connections.");
                var port = subset.ports.First().port;
                var protocol = subset.ports.First().name;
                foreach (var address in subset.addresses)
                {
                    ProxyListener listener = new ProxyListener(address.ip, port, protocol);
                    await listener.ConnectAsync();
                    PlatformMonitor.GetInstance().Register(listener);
                }
            }
            return Ok();
        }

        [HttpGet("api/listeners")]
        [AllowAnonymous]
        public IActionResult GetListeners()
        {
            var result = PlatformMonitor.GetInstance().Listeners;
            return Ok(result);
        }

        private async Task<User> GetUserFromSecurityServerAsync(string clientId, User user)
        {
            var result = await Security.ClerksAPI().GetByUsernameAsync(clientId, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Username.ToLower().Equals(user.Username.ToLower())) return userToCheck;
            }
            return null;
        }

        private async Task<User> GetUserFromEmailSecurityServerAsync(string clientId, User user)
        {
            var result = await Security.ClerksAPI().GetByEmailAsync(clientId, user);
            string jsonResult = (result as ObjectResult).Value.ToString();
            User[] users = Newtonsoft.Json.JsonConvert.DeserializeObject<User[]>(jsonResult);
            foreach (User userToCheck in users)
            {
                if (userToCheck.Email.ToLower().Equals(user.Email.ToLower())) return userToCheck;
            }
            return null;
        }

        [HttpGet("api/lotto/security/users/{userID}")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> GetLottoUserAsync(string userID)
        {
            return await Security.ClerksAPI().GetAgentsAsync(ClientLotto, userID);
        }

        [HttpGet("api/keno/security/users/{userID}")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> GetKenoUserAsync(string userID)
        {
            return await Security.ClerksAPI().GetAgentsAsync(ClientKeno, userID);
        }

        [HttpGet("api/lotto/security/users/{userID}/roles")]
        [Authorize(Roles = "ViewUsers")]
        public async Task<IActionResult> GetRolesOFLottoUserAsync(string userID)
        {
			var roles = await Security.ClerksAPI().GetRolesOFUserAsync(ClientLotto, userID);
			return Ok(roles);
        }

        [HttpGet("api/keno/security/users/{userID}/roles")]
        [Authorize(Roles = "KViewUsers")]
        public async Task<IActionResult> GetRolesOFKenoUserAsync(string userID)
        {
            var roles = await Security.ClerksAPI().GetRolesOFUserAsync(ClientKeno, userID);
            return Ok(roles);
        }

        [HttpPost("api/lotto/security/users/{userID}/roles")]
        [Authorize(Roles = "AssignRoles")]
        public async Task<IActionResult> MapRoleToLottoUsersAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().MapRoleToUsersAsync(ClientLotto, userID, roleMapping);
        }

        [HttpPost("api/keno/security/users/{userID}/roles")]
        [Authorize(Roles = "KAssignRoles")]
        public async Task<IActionResult> MapRoleToKenoUsersAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().MapRoleToUsersAsync(ClientKeno, userID, roleMapping);
        }

        [HttpPost("api/lotto/security/users/{userID}/logout")]
        [AllowAnonymous]
        public async Task<IActionResult> LottoLogoutAsync(string userID)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientLotto, userID);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(jsonResult);

                result = await Security.ClerksAPI().LogOutAsync(ClientLotto, userID);
                if (result is OkObjectResult)
                {
                    LogOutResponse output = new LogOutResponse();
                    output.HasEmail = user.HasEmail;
                    return Ok(output);
                }
            }

            result = await Security.PlayersAPI().GetAgentsAsync(ClientLotto, userID);
            if (result is OkObjectResult)
            {
                result = await Security.PlayersAPI().LogOutAsync(ClientLotto, userID);
            }

            return result;
        }

        [HttpPost("api/keno/security/users/{userID}/logout")]
        [AllowAnonymous]
        public async Task<IActionResult> KenoLogoutAsync(string userID)
        {
            var result = await Security.ClerksAPI().GetAgentsAsync(ClientKeno, userID);
            if (result is OkObjectResult)
            {
                string jsonResult = (result as ObjectResult).Value.ToString();
                User user = Newtonsoft.Json.JsonConvert.DeserializeObject<User>(jsonResult);

                result = await Security.ClerksAPI().LogOutAsync(ClientKeno, userID);
                if (result is OkObjectResult)
                {
                    LogOutResponse output = new LogOutResponse();
                    output.HasEmail = user.HasEmail;
                    return Ok(output);
                }
            }

            return result;
        }

        [HttpPut("api/lotto/security/users/{userID}/rolesremoval")]
        [Authorize(Roles = "AssignRoles")]
        public async Task<IActionResult> MapRoleToLottoUsersDeleteAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().DeleteMapRoleToUsersAsync(ClientLotto, userID, roleMapping);
        }

        [HttpPut("api/keno/security/users/{userID}/rolesremoval")]
        [Authorize(Roles = "KAssignRoles")]
        public async Task<IActionResult> MapRoleToKenoUsersDeleteAsync(string userID, [FromBody] RoleMapping[] roleMapping)
        {
            return await Security.ClerksAPI().DeleteMapRoleToUsersAsync(ClientKeno, userID, roleMapping);
        }

        [HttpPut("api/lotto/security/users/{userID}")]
        [Authorize(Roles = "AssignRoles")]
        public async Task<IActionResult> UpdateLottoUserAsync(string userID, [FromBody]User user)
        {
            User userValue = await GetUserFromSecurityServerAsync(ClientLotto, user);
            if (userValue != null)
            {
                if (userValue.Email != user.Email)
                {
					User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(ClientLotto, user);

					if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not updated because email {user.Email} was already taken.""}}");
                }
            }
            return await Security.ClerksAPI().UpdateUserAsync(ClientLotto, userID, user);
        }

        [HttpPut("api/keno/security/users/{userID}")]
        [Authorize(Roles = "KAssignRoles")]
        public async Task<IActionResult> UpdateKenoUserAsync(string userID, [FromBody] User user)
        {
            User userValue = await GetUserFromSecurityServerAsync(ClientKeno, user);
            if (userValue != null)
            {
                if (userValue.Email != user.Email)
                {
                    User emailSecuredUser = await GetUserFromEmailSecurityServerAsync(ClientKeno, user);

                    if (emailSecuredUser != null) return NotFound($@"{{""type"":""error"",""message"":""User was not updated because email {user.Email} was already taken.""}}");
                }
            }
            return await Security.ClerksAPI().UpdateUserAsync(ClientKeno, userID, user);
        }

        [HttpPut("api/lotto/security/users/{userID}/reset-password")]
        [Authorize(Roles = "AssignRoles")]
        public async Task<IActionResult> ResetPasswordLottoAsync(string userID, [FromBody]Password pass)
        {
            return await Security.ClerksAPI().UpdatePasswordAsync(ClientLotto, userID, pass);
        }

        [HttpPut("api/keno/security/users/{userID}/reset-password")]
        [Authorize(Roles = "KAssignRoles")]
        public async Task<IActionResult> ResetPasswordKenoAsync(string userID, [FromBody] Password pass)
        {
            return await Security.ClerksAPI().UpdatePasswordAsync(ClientKeno, userID, pass);
        }

        [HttpDelete("api/lotto/security/users/{userID}")]
        [Authorize(Roles = "DeleteUser")]
        public async Task<IActionResult> DeleteLottoUserAsync(string userID)
        {
            return await Security.ClerksAPI().DeleteUserAsync(ClientLotto, userID);
        }

        [HttpDelete("api/keno/security/users/{userID}")]
        [Authorize(Roles = "KDeleteUser")]
        public async Task<IActionResult> DeleteKenoUserAsync(string userID)
        {
            return await Security.ClerksAPI().DeleteUserAsync(ClientKeno, userID);
        }
    }
}

