using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;
using Newtonsoft.Json;

namespace GamesEngine.Exchange.town.connectors.drivers.artemis.tenant
{
	public class AccountInfo : DGSTenantDriver
	{
		private RestClient _getAccountInfoClient;

		public AccountInfo() : base(Tenant_Actions.Others)
		{
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			if (_getAccountInfoClient == null)
			{
				if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
				if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
				if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

				if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
				{
					await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
				}

				_getAccountInfoClient = new RestClient(ServicesUrl);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
				Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

				if (changeApplied)
				{
					if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
					{
						await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
					}

					_getAccountInfoClient = new RestClient(ServicesUrl);
				}
			}

			var AccountName = recordSet.Mappings["accountName"];
			string accountName = AccountName.AsString;

			var result = await GetAccountInfoAsync(accountName);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("accountName");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
			Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
			ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}

		async Task<AccountInfoBody> GetAccountInfoAsync(string accountName)
		{
			if (string.IsNullOrWhiteSpace(accountName)) throw new ArgumentNullException(nameof(accountName));
			if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

			string url = $"/players/{accountName}/details";

			string responseString = "";
			int retryNumber = 0;
			var result = new AccountInfoBody();
			while (true)
			{
				try
				{
					var request = new RestRequest(url, Method.GET);
					request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
					request.AddParameter("name", accountName);
					IRestResponse response = await _getAccountInfoClient.ExecuteAsync(request);
					responseString = response.Content;
					if ((int)response.StatusCode != 200)
					{
						Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetAccountInfoAsync)}\nUrl:{url}\nResponse: {responseString}");
					}
					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesDGS.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					await Task.Delay(5000);
					if (retryNumber == MAX_RETRIES) return result;
				}
			}

			if (string.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetAccountInfoAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
				return result;
			}
			else
			{
				// First try to parse as error response
				var errorResponse = Commons.FromJson<AccountInfoErrorResponse>(responseString);
				if (errorResponse != null && (!string.IsNullOrWhiteSpace(errorResponse.Type) || !string.IsNullOrWhiteSpace(errorResponse.Title) || !string.IsNullOrWhiteSpace(errorResponse.Detail)))
				{
					Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetAccountInfoAsync)}\nUrl:{url}\nResponse: {responseString}");
				}
				else
				{
					// Try to parse as successful response
					try
					{
						var responseObj = Commons.FromJson<AccountInfoBody>(responseString);
						result = responseObj;
					}
					catch (Exception e)
					{
						NotifyWarn(nameof(GetAccountInfoAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
					}
				}
				return result;
			}
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public class AccountInfoBody
		{
			[JsonProperty("idPlayer")]
			public int IdPlayer { get; set; }

			[JsonProperty("name")]
			public string Name { get; set; }

			[JsonProperty("address")]
			public string Address { get; set; }

			[JsonProperty("phone")]
			public string Phone { get; set; }

			[JsonProperty("email")]
			public string Email { get; set; }
		}

		public class AccountInfoErrorResponse
		{
			[JsonProperty("type")]
			public string Type { get; set; }

			[JsonProperty("title")]
			public string Title { get; set; }

			[JsonProperty("status")]
			public int Status { get; set; }

			[JsonProperty("detail")]
			public string Detail { get; set; }

			[JsonProperty("instance")]
			public string Instance { get; set; }

			[JsonProperty("additionalProp1")]
			public string AdditionalProp1 { get; set; }

			[JsonProperty("additionalProp2")]
			public string AdditionalProp2 { get; set; }

			[JsonProperty("additionalProp3")]
			public string AdditionalProp3 { get; set; }
		}
	}
}
