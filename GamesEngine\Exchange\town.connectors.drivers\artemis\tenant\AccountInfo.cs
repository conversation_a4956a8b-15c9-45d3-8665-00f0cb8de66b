using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using town.connectors;
using town.connectors.commons;
using town.connectors.drivers;
using town.connectors.drivers.artemis;
using Puppeteer.EventSourcing;

namespace GamesEngine.Exchange.town.connectors.drivers.artemis.tenant
{
	public class AccountInfo : DGSTenantDriver
	{
		private RestClient _getAccountInfoClient;

		public AccountInfo() : base(Tenant_Actions.Others)
		{
		}
		public override async Task<T> ExecuteAsync<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			if (_getAccountInfoClient == null)
			{
				if (string.IsNullOrWhiteSpace(SystemId)) throw new ArgumentNullException("Custom variable 'TokenSystemId' is required");
				if (string.IsNullOrWhiteSpace(Password)) throw new ArgumentNullException("Custom variable 'TokenSystemPassword' is required");
				if (string.IsNullOrWhiteSpace(ServicesUrl)) throw new ArgumentNullException("Custom variable 'CompanyBaseUrlServices' is required");

				if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
				{
					await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
				}

				_getAccountInfoClient = new RestClient(ServicesUrl);
			}

			if (CustomSettings.ThereArePendingChanges)
			{
				bool changeApplied = false;
				ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices", out changeApplied).AsString;
				SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
				Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();

				if (changeApplied)
				{
					if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken))
					{
						await DGSTenantDriver.RequestAppTokenAsync(ServicesUrl, SystemId, Password);
					}

					_getAccountInfoClient = new RestClient(ServicesUrl);
				}
			}

			var AccountName = recordSet.Mappings["accountName"];
			string accountName = AccountName.AsString;

			var result = await GetAccountInfoAsync(accountName);
			return (T)Convert.ChangeType(result, typeof(T));
		}

		public override void Prepare(DateTime now)
		{
			CustomSettings.AddVariableParameter("accountName");

			//CustomSettings.Prepare();

			SystemId = CustomSettings.Get(now, "TokenSystemId").AsString;
			Password = CustomSettings.Get(now, "TokenSystemPassword").AsSecret.ToString();
			ServicesUrl = CustomSettings.Get(now, "CompanyBaseUrlServices").AsString;
		}

		async Task<AccountInfoBody> GetAccountInfoAsync(string accountName)
		{
			if (string.IsNullOrWhiteSpace(accountName)) throw new ArgumentNullException(nameof(accountName));
			if (string.IsNullOrWhiteSpace(DGSProcessorDriver.AppToken)) throw new Exception($"{nameof(DGSProcessorDriver.AppToken)} is empty");

			string url = $"/AccountInfo";

			string responseString = "";
			int retryNumber = 0;
			var result = new AccountInfoBody();
			while (true)
			{
				try
				{
					var request = new RestRequest(url, Method.GET);
					request.AddHeader("Authorization", $"Bearer {DGSProcessorDriver.AppToken}");
					request.AddParameter("name", accountName);
					IRestResponse response = await _getAccountInfoClient.ExecuteAsync(request);
					responseString = response.Content;
					if ((int)response.StatusCode != 200)
					{
						Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetAccountInfoAsync)}\nUrl:{url}\nResponse: {responseString}");
					}
					break;
				}
				catch (Exception e)
				{
					Loggers.GetIntance().AccountingServicesDGS.Error($@"url:{url} type:{e.GetType()} error:{e.Message}", e);

					retryNumber++;

					await Task.Delay(5000);
					if (retryNumber == MAX_RETRIES) return result;
				}
			}

			if (string.IsNullOrWhiteSpace(responseString))
			{
				NotifyWarn(nameof(GetAccountInfoAsync), $"Url:{url}\nResponse: {responseString}", $"Response can not be empty");
				return result;
			}
			else
			{

				var errorResponse = Commons.FromJson<ErrorResponse>(responseString);
				if (errorResponse != null && !string.IsNullOrWhiteSpace(errorResponse.Message))
				{
					Loggers.GetIntance().AccountingServicesDGS.Debug($"{nameof(GetAccountInfoAsync)}\nUrl:{url}\nResponse: {responseString}");
				}
				else
				{
					try
					{
						var responseObj = Commons.FromJson<AccountInfoBody>(responseString);
						result = responseObj;
					}
					catch (Exception e)
					{
						NotifyWarn(nameof(GetAccountInfoAsync), $"Url:{url}\nResponse: {responseString}", $"{e.Message}\r{nameof(responseString)} {responseString} is not valid");
					}
				}
				return result;
			}
		}

		public override T Execute<T>(DateTime now, CustomSettings.RecordSet recordSet)
		{
			throw new NotImplementedException();
		}

		public class AccountInfoBody
		{
			public int idPlayer { get; set; }
			public string name { get; set; }
			public string address { get; set; }
			public string phone { get; set; }
			public string email { get; set; }
		}
	}
}
