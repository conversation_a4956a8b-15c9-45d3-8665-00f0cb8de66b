using CashierAPI.Controllers;
using ExternalServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using town.connectors.drivers.artemis;

namespace ASIStub.Controllers
{
	public class DGSStub : AuthorizeController
	{
		static int authorization = 1000;

		[HttpGet("/players/{customerId}/balance")]
		[AllowAnonymous]
		public NewPlayerBalanceResponse GetPlayerBalance(int customerId)
		{
			var currentUser = HttpContext.User;
			var result = new NewPlayerBalanceResponse()
			{
				playerId = customerId,
				playerName = $"Player{customerId}",
				agentId = 1,
				agentName = "agent1",
				currentBalance = 101966.61m,
				availableBalance = 101966.61m,
				amountAtRisk = 0m,
				realAvailableBalance = 101966.61m,
				creditLimit = 0m,
				freePlayAmount = 0m,
				thisWeek = 0m,
				lastWeek = 0m,
				bonusPoints = 0m
			};
			return result;
		}

		[HttpPost("/InsertDebitTransaction")]
		[AllowAnonymous]
		public DebitTransactionResponse InsertDebitTransaction([FromBody] DebitTransactionBody body)
		{
			var result = new DebitTransactionResponse()
			{
				amount = body.amount,
				bonus = body.bonus,
				description = body.description,
				fee = body.fee,
				idPlayer = body.idPlayer,
				idTransaction = body.idTransaction,
				referenceId = body.referenceId,
				tickets = body.tickets
			};

			foreach (var ticket in result.tickets)
			{
				authorization++;
				ticket.ticketId = authorization.ToString();
				ticket.toWin /= 2;
			}
			return result;
		}

		[HttpPost("/InsertCreditTransaction")]
		[AllowAnonymous]
		public CreditTransactionBody InsertCreditTransaction([FromBody] CreditTransactionBody body)
		{
			authorization++;
			body.idTransaction = authorization;
			return body;
		}

		[HttpGet("/players/{customerId}/validation-status")]
		[AllowAnonymous]
		public IActionResult ValidateCustomer(string customerId, string token)
		{
			// Return the new JSON response format
			return Ok(new { isValid = true });
		}

		[HttpPost("/Auth/Login")]
		[AllowAnonymous]
		public IActionResult Login([FromBody] AppLoginBody user)
		{
			var tokenString = GenerateJSONWebToken(user);
			var response = Ok(new { token = tokenString });
			return response;
		}

		string GenerateJSONWebToken(AppLoginBody userInfo)
		{
			var claims = new[] {
				new Claim(JwtRegisteredClaimNames.Sub, userInfo.appId ?? "test"),
				new Claim(JwtRegisteredClaimNames.Email, "<EMAIL>"),
				new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
			};
			var key = Encoding.ASCII.GetBytes($"3aab83fb_38f845e977af7f2cd6313c5a9d35e4de7f353405100e7748ab15b410eb53d886");
			var token = new JwtSecurityToken(null,
				null,
				claims,
				expires: DateTime.UtcNow.AddSeconds(60 * 60), 
				signingCredentials: new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
				);

			return new JwtSecurityTokenHandler().WriteToken(token);
		}

		[HttpPost("/CreateWagers")]
		[AllowAnonymous]
		public IActionResult CreateWagersCollection([FromBody] WagersCreationBody body)
		{
			var response = Ok(new WagersCreationResponse());
			return response;
	}
		[HttpPost("/UpdateWagers")]
		[AllowAnonymous]
		public IActionResult UpdateWagersCollection([FromBody] WagersUpdateBody body)
		{
			var response = Ok(new WagersCreationResponse());
			return response;
		}
	}
}
